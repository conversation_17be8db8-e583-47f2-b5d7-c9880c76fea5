---
description: 
globs: 
alwaysApply: false
---
# Sistema de Cursores - Projeto Freedom

## Localização e Estrutura

- **Módulo**: [cursores-zk](mdc:crmservice/crmservice/crmservice/cursores-zk/src/main/java/freedom/bytecode/cursor)
- **<PERSON><PERSON><PERSON>**: `NOME_DO_CURSOR.java`
- **?? CRÍTICO**: Cursores NÃO podem ser criados ou editados, apenas utilizados

## Padrões de Uso
### caso o cursor não esteja instanciado em FrmNome.java, Instanciação (sempre no construtor ou métodos da RNA)
```java
CURSOR_NAME tbNomeInstancia = new CURSOR_NAME("nomeInstancia");
```

## IMPORTANTE
ao encontrar a classe do cursor CURSOR_NAME
buscar e obter oq for necessário, na classe tem todos os campos (get,set) e tabém todos os filtros (setFilterName)

### Operações Comuns
```java
// Ciclo básico
tbCursor.open();           // Abrir cursor
tbCursor.first();          // Primeiro registro
tbCursor.next();           // Próximo registro  
tbCursor.close();          // Fechar cursor

// Filtragem e consulta
//com nome no metodo (preferivel)
tbCursor.setFilterFILTER_NAME(params);  // Filtrar dados
//filtro sem nome
tbCursor.addFilter(NOME_FILTRO);
tbCursor.addParam(params);

// Acesso aos dados
//com nome (preferivel)
tbCursor.getCAMPO_NAME();
tbCursor.setCAMPO_NAME(valor);
//sem nome
tbCursor.getFieldValue("CAMPO_NAME");
tbCursor.setFieldValue("CAMPO_NAME", valor);
```

### Exemplos de Cursores Frequentes

**CRM e Clientes:**
- `CLIENTES_FROTA` - Dados da frota de clientes
- `CLIENTE_DIVERSO` - Informações adicionais de clientes
- `CLIENTE_CONTATO` - Contatos dos clientes

**Produtos e Catálogo:**
- `PRODUTOS` - Catálogo de produtos
- `PRODUTOS_MODELOS` - Modelos de produtos
- `MARCAS` - Marcas disponíveis

**Ordem de Serviço:**
- `OS_AGENDA` - Agendamentos de OS
- `OS_ITEM_GRID` - Itens da OS
- `MOB_OS_PERTENCE` - Relacionamentos OS

**Documentos:**
- `DOC_POR_CHASSI` - Documentos por chassi

## Estrutura Interna dos Cursores

Cada cursor contém:
```java
public final class NOME_CURSOR extends TFTable {
    // Construtor obrigatório com instanceName
    public NOME_CURSOR(String instanceName) {
        setName(instanceName);
        setCursor("id_numerico");   
        setTableName("NOME_TABELA");
        
        // Definição dos campos
        TFTableField campo = new TFTableField();
        campo.setName("NOME_CAMPO");
        campo.setFieldType("ftString|ftInteger|ftDecimal|ftDate");
        campo.setPrimaryKey(true|false);
        // ...
    }
}
```

## Restrições Importantes

### ? IA NÃO PODE:
- Criar novos cursores
- Editar cursores existentes
- Alterar estrutura de campos
- Modificar módulo cursores-zk

### ? IA PODE:
- Usar cursores existentes nas classes RNA
- Instanciar cursores já definidos
- Chamar métodos dos cursores
- Filtrar e consultar dados

## Padrões de Busca

Para encontrar cursores disponíveis:
```bash
# Buscar por nome específico
find cursores-zk/ -name "*CLIENTE*"

# Listar todos os cursores
ls cursores-zk/src/main/java/freedom/bytecode/cursor/

# Buscar uso de cursor em RNA
grep -r "new NOME_CURSOR" crmservice/src/main/java/freedom/bytecode/rn/
```

## Exemplo Prático

No [FrotaClienteRNA.java](mdc:crmservice/crmservice/crmservice/crmservice/src/main/java/freedom/bytecode/rn/FrotaClienteRNA.java):
```java
// Instanciação
CLIENTE_DIVERSO tbClienteDiversos = new CLIENTE_DIVERSO("tbClienteDiversos");

// Uso em método
public String getNomeCliente(Double codCliente) throws DataException {
    tbClienteDiversos.filtrar("COD_CLIENTE", codCliente);
    if (tbClienteDiversos.exists()) {
        return tbClienteDiversos.getFieldValue("NOME").toString();
    }
    return "";
}
```



