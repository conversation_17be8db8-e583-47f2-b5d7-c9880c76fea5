/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: ********
   Class Main  : EmBrancoParaTestes
   Analista    : LUIZ.RIPARDO
   Data Created: 20/08/2025 11:44:00
   Data Changed: 20/08/2025 11:47:55
   Data Geracao: 20/08/2025 16:16:02
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class EmBrancoParaTestesRNW extends EmBrancoParaTestesRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



