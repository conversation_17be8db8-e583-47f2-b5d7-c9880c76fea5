package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmEmBrancoParaTestes extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.EmBrancoParaTestesRNA rn = null;

    public FrmEmBrancoParaTestes() {
        try {
            rn = (freedom.bytecode.rn.EmBrancoParaTestesRNA) getRN(freedom.bytecode.rn.wizard.EmBrancoParaTestesRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbClienteDiverso();
        init_FVBox1();
        init_btnTeste();
        init_FGrid1();
        init_FrmEmBrancoParaTestes();
    }

    public CLIENTE_DIVERSO tbClienteDiverso;

    private void init_tbClienteDiverso() {
        tbClienteDiverso = rn.tbClienteDiverso;
        tbClienteDiverso.setName("tbClienteDiverso");
        tbClienteDiverso.setMaxRowCount(200);
        tbClienteDiverso.setWKey("572025;57201");
        tbClienteDiverso.setRatioBatchSize(20);
        getTables().put(tbClienteDiverso, "tbClienteDiverso");
        tbClienteDiverso.applyProperties();
    }

    protected TFForm FrmEmBrancoParaTestes = this;
    private void init_FrmEmBrancoParaTestes() {
        FrmEmBrancoParaTestes.setName("FrmEmBrancoParaTestes");
        FrmEmBrancoParaTestes.setCaption("EmBrancoParaTestes");
        FrmEmBrancoParaTestes.setClientHeight(295);
        FrmEmBrancoParaTestes.setClientWidth(449);
        FrmEmBrancoParaTestes.setColor("clBtnFace");
        FrmEmBrancoParaTestes.setWOrigem("EhMain");
        FrmEmBrancoParaTestes.setWKey("572025");
        FrmEmBrancoParaTestes.setSpacing(0);
        FrmEmBrancoParaTestes.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(449);
        FVBox1.setHeight(295);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmEmBrancoParaTestes.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFButton btnTeste = new TFButton();

    private void init_btnTeste() {
        btnTeste.setName("btnTeste");
        btnTeste.setUploadMime("image/*");
        btnTeste.setLeft(0);
        btnTeste.setTop(0);
        btnTeste.setWidth(75);
        btnTeste.setHeight(25);
        btnTeste.setCaption("Teste");
        btnTeste.setFontColor("clWindowText");
        btnTeste.setFontSize(-11);
        btnTeste.setFontName("Tahoma");
        btnTeste.setFontStyle("[]");
        btnTeste.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnTesteClick(event);
            processarFlow("FrmEmBrancoParaTestes", "btnTeste", "OnClick");
        });
        btnTeste.setImageId(0);
        btnTeste.setColor("clBtnFace");
        btnTeste.setAccess(false);
        btnTeste.setIconClass("a fa-solid fa-earth-africa");
        btnTeste.setIconReverseDirection(false);        FVBox1.addChildren(btnTeste);
        btnTeste.applyProperties();
    }

    public TFGrid FGrid1 = new TFGrid();

    private void init_FGrid1() {
        FGrid1.setName("FGrid1");
        FGrid1.setLeft(0);
        FGrid1.setTop(26);
        FGrid1.setWidth(320);
        FGrid1.setHeight(120);
        FGrid1.setTable(tbClienteDiverso);
        FGrid1.setFlexVflex("ftTrue");
        FGrid1.setFlexHflex("ftTrue");
        FGrid1.setPagingEnabled(false);
        FGrid1.setFrozenColumns(0);
        FGrid1.setShowFooter(false);
        FGrid1.setShowHeader(true);
        FGrid1.setMultiSelection(false);
        FGrid1.setGroupingEnabled(false);
        FGrid1.setGroupingExpanded(false);
        FGrid1.setGroupingShowFooter(false);
        FGrid1.setCrosstabEnabled(false);
        FGrid1.setCrosstabGroupType("cgtConcat");
        FGrid1.setEditionEnabled(false);
        FGrid1.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("COD_CLIENTE");
        item0.setTitleCaption("C\u00F3d. Cliente");
        item0.setWidth(78);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        FGrid1.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("NOME");
        item1.setTitleCaption("Nome");
        item1.setWidth(166);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        FGrid1.getColumns().add(item1);
        FVBox1.addChildren(FGrid1);
        FGrid1.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnTesteClick(final Event<Object> event) {
        if (btnTeste.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnTeste");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}