---
description: 
globs: 
alwaysApply: false
---
# Workflow para Alteração de Telas - Projeto Freedom

## Fluxo de Decisão Principal

### 1️⃣ PRIMEIRA VERIFICAÇÃO: Analisar FrmNome.java

**SEMPRE verificar primeiro se já existe:**
- Componente necessário: `public TFComponente nomeComponente = new TFComponente();`
- Método abstrato: `public abstract void nomeComponenteEvento(final Event<Object> event);`
- Cursor necessário: `public NOME_CURSOR tbNomeCursor;`

### 2️⃣ SE JÁ EXISTE → Implementar apenas lógica

**✅ PODE ALTERAR:**
- ](mdc:crmservice/src/main/java/freedom/bytecode/form) - Implementação dos eventA.java](mdc:crmservice/src/main/java/freedom/bytecode/rn) - Regras de negócio

### 3️⃣ SE NÃO EXISTE → Verificar tipo de adição

#### 📊 CURSOR (IA NÃO PODE ADICIONAR)
```
🚫 PARAR - Solicitar intervenção manual

Resposta obrigatória:
"ATENÇÃO: É necessário adicionar o cursor NOME_CURSOR ao arquivo .frm. 
Como IA, não tenho permissão para alterar cursores na estrutura base. 
Solicitação de intervenção manual necessária."
```

#### 🎨 COMPONENTE (IA PODE ADICIONAR)
```
✅ PODE ALTERAR o arquivo .frm para adicionar componente
Consultar documentação de componentes disponíveis
Adicionar ao design/FrmNomeTela.frm
```

## Templates de Resposta

### ✅ Quando pode implementar:
```
ANÁLISE CONCLUÍDA ✅
VERIFICAÇÃO: Componente/evento já existe em FrmNome.java
AÇÃO: Implementando alterações em:
- FrmNomeA.java: [métodos que serão alterados]  
- NomeRNA.java: [regras que serão adicionadas]
CURSORES UTILIZADOS: [lista dos cursores existentes]
```

### 🚫 Quando precisa de intervenção:
```
ANÁLISE CONCLUÍDA ⚠️
VERIFICAÇÃO: Cursor NOME_CURSOR não existe em FrmNome.java
AÇÃO NECESSÁRIA: Adição manual de cursor ao arquivo .frm
JUSTIFICATIVA: IA não tem permissão para alterar estrutura de cursores
```

## Regras de Segurança

- **NUNCA alterar** arquivos wizard (FrmNomeW.java, NomeRNW.java)
- **NUNCA alterar** classes abstratas geradas automaticamente
- **SEMPRE verificar** existência antes de implementar

- **SEMPRE usar** cursores já definidos na classe base