---
description: 
globs: 
alwaysApply: false
---
# Arquitetura do Projeto Freedom

## Estrutura Geral

O projeto segue uma arquitetura em camadas baseada no framework Freedom com separação clara entre definição, apresentação e lógica de negócio:

```mermaid
?? .frm (Design)     ?  ?? .java (Base)      ?  ?? A.java (UI)       ?  ?? RNA.java (Business)
  Gerado Auto           Abstract Generated       Event Logic           Rules Logic
       ?                       ?                      ?                      ?
?? Wizard Classes      ?? Cursores (cursores-zk - Somente Leitura)
  Intermediários         Acesso aos Dados
```

## Hierarquia de Classes

### Forms (Interface/Tela)
```java
TFForm (Freedom Framework)
  ? FrmNomeTela.java (Abstract - Gerado automaticamente do .frm)
    ? FrmNomeTelaW.java (Abstract - Wizard intermediário) 
      ? FrmNomeTelaA.java (Concreta - Implementação real)
```

### Regras de Negócio
```java
IRegraNegocio (Freedom Framework)
  ? NomeTelaRN.java (Abstract - Gerado automaticamente)
    ? NomeTelaRNW.java (Abstract - Wizard intermediário)
      ? NomeTelaRNA.java (Concreta - Implementação real)
```

## Módulos Principais

- **crmservice/**: Aplicação principal com telas e regras de negócio
- **cursores-zk/**: Módulo de cursores (somente leitura)
- **nbs-util-zk/**: Utilitários compartilhados
- **nbs-empresa-zk/**: Funcionalidades específicas de empresa

## Arquivos por Tela

Para cada tela existe:
- `design/FrmNomeTela.frm` - Design da tela (não editar)
- `freedom/bytecode/form/wizard/FrmNomeTela.java` - Base abstract (gerado)
- `freedom/bytecode/form/wizard/FrmNomeTelaW.java` - Wizard intermediário
- `freedom/bytecode/form/FrmNomeTelaA.java` - Implementação concreta
- `freedom/bytecode/rn/NomeTelaRNA.java` - Regras de negócio


