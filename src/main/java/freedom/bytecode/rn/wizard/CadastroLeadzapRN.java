package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class CadastroLeadzapRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public CadastroLeadzapRN() {
        init_tbCadastroWhatsapp();
        init_tbWhatsappEmpresa();
        init_tbEmpresas();
        init_tbWhatsappParametrosEmpresa();
        init_tbCadastroWhatsappValidar();
        init_tbEmpresasCruzaLeadZap();
        init_tbWhatsappLog();
        init_tbCadWhatsappAtivo();
        init_tbUf();
        init_tbCidades();
        init_tbCadastroWhatsappCidades();
        init_tbLeadzapMenu();
        init_scCrmCadastroWhatsapp();
    }

    public CRM_CADASTRO_WHATSAPP tbCadastroWhatsapp = new CRM_CADASTRO_WHATSAPP("tbCadastroWhatsapp");

    private void init_tbCadastroWhatsapp() {
        tbCadastroWhatsapp.setName("tbCadastroWhatsapp");
        tbCadastroWhatsapp.setMaxRowCount(200);
        tbCadastroWhatsapp.setWKey("440093;44001");
        tbCadastroWhatsapp.setRatioBatchSize(20);
    }

    public CRM_WHATSAPP_EMPRESA tbWhatsappEmpresa = new CRM_WHATSAPP_EMPRESA("tbWhatsappEmpresa");

    private void init_tbWhatsappEmpresa() {
        tbWhatsappEmpresa.setName("tbWhatsappEmpresa");
        tbWhatsappEmpresa.setMasterFields("ID_CELULAR");
        tbWhatsappEmpresa.setDetailFilters("ID_CELULAR");
        tbWhatsappEmpresa.setMaxRowCount(200);
        tbWhatsappEmpresa.setMasterTable(tbCadastroWhatsapp);
        tbWhatsappEmpresa.setWKey("440093;46001");
        tbWhatsappEmpresa.setRatioBatchSize(20);
    }

    public EMPRESAS tbEmpresas = new EMPRESAS("tbEmpresas");

    private void init_tbEmpresas() {
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(200);
        tbEmpresas.setWKey("440093;44002");
        tbEmpresas.setRatioBatchSize(20);
    }

    public WHATSAPP_PARAMETROS_EMPRESA tbWhatsappParametrosEmpresa = new WHATSAPP_PARAMETROS_EMPRESA("tbWhatsappParametrosEmpresa");

    private void init_tbWhatsappParametrosEmpresa() {
        tbWhatsappParametrosEmpresa.setName("tbWhatsappParametrosEmpresa");
        tbWhatsappParametrosEmpresa.setMaxRowCount(200);
        tbWhatsappParametrosEmpresa.setWKey("440093;44003");
        tbWhatsappParametrosEmpresa.setRatioBatchSize(20);
    }

    public CRM_CADASTRO_WHATSAPP tbCadastroWhatsappValidar = new CRM_CADASTRO_WHATSAPP("tbCadastroWhatsappValidar");

    private void init_tbCadastroWhatsappValidar() {
        tbCadastroWhatsappValidar.setName("tbCadastroWhatsappValidar");
        tbCadastroWhatsappValidar.setMaxRowCount(200);
        tbCadastroWhatsappValidar.setWKey("440093;44004");
        tbCadastroWhatsappValidar.setRatioBatchSize(20);
    }

    public EMPRESAS tbEmpresasCruzaLeadZap = new EMPRESAS("tbEmpresasCruzaLeadZap");

    private void init_tbEmpresasCruzaLeadZap() {
        tbEmpresasCruzaLeadZap.setName("tbEmpresasCruzaLeadZap");
        tbEmpresasCruzaLeadZap.setMaxRowCount(200);
        tbEmpresasCruzaLeadZap.setWKey("440093;46002");
        tbEmpresasCruzaLeadZap.setRatioBatchSize(20);
    }

    public CRM_WHATSAPP_LOG tbWhatsappLog = new CRM_WHATSAPP_LOG("tbWhatsappLog");

    private void init_tbWhatsappLog() {
        tbWhatsappLog.setName("tbWhatsappLog");
        tbWhatsappLog.setMaxRowCount(0);
        tbWhatsappLog.setWKey("440093;46003");
        tbWhatsappLog.setRatioBatchSize(20);
    }

    public CRM_CAD_WHATSAPP_ATIVO tbCadWhatsappAtivo = new CRM_CAD_WHATSAPP_ATIVO("tbCadWhatsappAtivo");

    private void init_tbCadWhatsappAtivo() {
        tbCadWhatsappAtivo.setName("tbCadWhatsappAtivo");
        tbCadWhatsappAtivo.setMaxRowCount(200);
        tbCadWhatsappAtivo.setWKey("440093;46004");
        tbCadWhatsappAtivo.setRatioBatchSize(20);
    }

    public UF tbUf = new UF("tbUf");

    private void init_tbUf() {
        tbUf.setName("tbUf");
        tbUf.setMaxRowCount(200);
        tbUf.setWKey("440093;46005");
        tbUf.setRatioBatchSize(20);
    }

    public CIDADES tbCidades = new CIDADES("tbCidades");

    private void init_tbCidades() {
        tbCidades.setName("tbCidades");
        tbCidades.setMaxRowCount(200);
        tbCidades.setWKey("440093;46006");
        tbCidades.setRatioBatchSize(20);
    }

    public CRM_CADASTRO_WHATSAPP_CIDADES tbCadastroWhatsappCidades = new CRM_CADASTRO_WHATSAPP_CIDADES("tbCadastroWhatsappCidades");

    private void init_tbCadastroWhatsappCidades() {
        tbCadastroWhatsappCidades.setName("tbCadastroWhatsappCidades");
        tbCadastroWhatsappCidades.setMaxRowCount(200);
        tbCadastroWhatsappCidades.setWKey("440093;46007");
        tbCadastroWhatsappCidades.setRatioBatchSize(20);
    }

    public CRM_LEADZAP_MENU tbLeadzapMenu = new CRM_LEADZAP_MENU("tbLeadzapMenu");

    private void init_tbLeadzapMenu() {
        tbLeadzapMenu.setName("tbLeadzapMenu");
        tbLeadzapMenu.setMaxRowCount(200);
        tbLeadzapMenu.setWKey("440093;46008");
        tbLeadzapMenu.setRatioBatchSize(20);
    }


    public TFSchema scCrmCadastroWhatsapp = new TFSchema();

    private void init_scCrmCadastroWhatsapp() {
        scCrmCadastroWhatsapp.setName("scCrmCadastroWhatsapp");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbCadastroWhatsapp);
        scCrmCadastroWhatsapp.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbWhatsappEmpresa);
        scCrmCadastroWhatsapp.getTables().add(item1);
        TFSchemaItem item2 = new TFSchemaItem();
        item2.setTable(tbCadastroWhatsappCidades);
        scCrmCadastroWhatsapp.getTables().add(item2);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}