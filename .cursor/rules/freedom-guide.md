# Guia Principal - Projeto Freedom

## 🎯 Visão Geral

Este é um projeto baseado no **Framework Freedom** com arquitetura em camadas para desenvolvimento de telas e regras de negócio. As regras abaixo devem ser seguidas rigorosamente.

## 📚 Documentação Completa

### 🏗️ @Arquitetura
- Estrutura geral do projeto
- Hierarquia de classes
- Módulos e organização de arquivos
- Padrões de nomenclatura

### 🔄 @Workflow
- **CRÍTICO**: Fluxo de decisão para alterações
- Templates de resposta obrigatórios
- Processo de verificação antes de implementar
- Regras de quando pode/não pode alterar

### 📊 @Cursores
- Sistema de cursores (somente leitura)
- Padrões de uso e operações
- Exemplos de cursores frequentes
- Restrições de acesso

### ⚠️ @Restrições
- **OBRIGATÓRIO**: O que a IA NÃO pode alterar
- Sinais de alerta para parar operação
- Checklist de verificação
- Proteções de segurança

### 💡 @Exemplos
- Templates de código prontos
- Padrões comuns de implementação
- Cenários típicos e soluções
- Referências de arquivos existentes

## 🚨 PROCESSO OBRIGATÓRIO

### 1️⃣ ANTES DE QUALQUER ALTERAÇÃO:

```
1. Ler as @Restrições
2. Seguir o @Workflow
3. Verificar classe base FrmNome.java
4. Confirmar que não precisa de novos cursores
5. Só então implementar usando @Exemplos
```

### 2️⃣ VERIFICAÇÃO DE SEGURANÇA:

- ✅ **Arquivo termina com 'A'?** (implementação concreta)
- ✅ **Não está em cursores-zk?**
- ✅ **Não é classe wizard (termina com 'W')?**
- ✅ **Componente/evento já existe na classe base?**

**Se qualquer item for ❌, PARAR e seguir @Restrições**

## 🎯 Tipos de Solicitação

### ✅ PODE IMPLEMENTAR DIRETAMENTE:
- Alterar lógica de eventos existentes
- Adicionar validações
- Modificar regras de negócio
- Usar cursores já definidos

### ⚠️ PRECISA VERIFICAR PRIMEIRO:
- Adicionar novos componentes UI
- Alterar layout da tela
- Criar novos eventos

### ❌ NÃO PODE FAZER:
- Criar/editar cursores
- Alterar classes wizard
- Modificar arquivos .frm para cursores
- Editar classes abstratas geradas

## 📋 Template de Análise

Para toda solicitação, usar este formato:

```
ANÁLISE INICIAL:
- Tela: [nome da tela]
- Arquivos envolvidos: [listar]
- Verificação de segurança: [checklist]

VERIFICAÇÃO DA CLASSE BASE:
- Componentes necessários: [existem/não existem]
- Eventos necessários: [existem/não existem]
- Cursores necessários: [existem/não existem]

DECISÃO:
[✅ Implementar / ⚠️ Verificar / ❌ Não permitido]

AÇÃO:
[Detalhar o que será feito ou motivo da restrição]
```

## 🔍 Comandos Úteis para Análise

```bash
# Encontrar tela específica
find . -name "*NomeTela*"

# Verificar cursores disponíveis
ls cursores-zk/src/main/java/freedom/bytecode/cursor/

# Buscar uso de cursor
grep -r "new NOME_CURSOR" crmservice/src/main/java/

# Verificar hierarquia de classe
grep "extends.*" arquivo.java
```

## 💼 Responsabilidades da IA

1. **Seguir rigorosamente** todas as restrições
2. **Verificar sempre** antes de implementar
3. **Usar templates** das documentações
4. **Documentar** alterações realizadas
5. **Solicitar intervenção** quando necessário

## 🎪 Exemplo de Fluxo Completo

**Solicitação**: "Adicionar validação de email na tela de Cliente"

**Processo**:
1. 📖 Consultar @Restrições
2. 🔄 Seguir @Workflow
3. 🔍 Verificar FrmCliente.java → ✅ Evento btnSalvarClick existe
4. 💡 Usar @Exemplos para implementar
5. ✅ Implementar em FrmClienteA.java

**Resultado**: Alteração segura e dentro dos padrões do projeto.

---

**⚠️ IMPORTANTE**: Sempre que houver dúvida, consultar as regras específicas. A documentação é sua fonte da verdade para trabalhar com segurança no projeto Freedom.
